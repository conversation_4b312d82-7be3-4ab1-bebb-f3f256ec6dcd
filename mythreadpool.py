# -*- coding: utf-8 -*-
import threading
import time
from typing import List, Dict, Any, Optional, Callable
from mythread import MyThread, ThreadState
from concurrent.futures import Future
import queue


class MyThreadPool:
    """基于MyThread的线程池类，支持暂停、恢复、销毁等高级控制功能"""
    
    def __init__(self, max_workers: int = 4, thread_name_prefix: str = "MyThreadPool"):
        """
        初始化线程池
        
        :param max_workers: 最大工作线程数
        :param thread_name_prefix: 线程名称前缀
        """
        self.max_workers = max_workers
        self.thread_name_prefix = thread_name_prefix
        self._threads: List[MyThread] = []
        self._task_queue = queue.Queue()
        self._futures: Dict[int, Future] = {}
        self._lock = threading.Lock()
        self._shutdown = False
        self._task_counter = 0
        
        # 创建工作线程
        self._create_workers()
    
    def _create_workers(self):
        """创建工作线程"""
        for i in range(self.max_workers):
            thread_name = f"{self.thread_name_prefix}-{i}"
            worker_thread = MyThread(
                target=self._worker_loop,
                name=thread_name,
                daemon=True
            )
            self._threads.append(worker_thread)
            worker_thread.start()
    
    def _worker_loop(self):
        """工作线程循环"""
        while not self._shutdown:
            try:
                # 从任务队列获取任务，超时1秒
                task_id, func, args, kwargs, future = self._task_queue.get(timeout=1)
                
                try:
                    # 执行任务
                    result = func(*args, **kwargs)
                    future.set_result(result)
                except Exception as e:
                    future.set_exception(e)
                finally:
                    # 清理已完成的future
                    with self._lock:
                        if task_id in self._futures:
                            del self._futures[task_id]
                    self._task_queue.task_done()
                    
            except queue.Empty:
                # 队列为空，继续循环
                continue
            except Exception as e:
                print(f"工作线程异常: {e}")
    
    def submit(self, func: Callable, *args, **kwargs) -> Future:
        """
        提交任务到线程池
        
        :param func: 要执行的函数
        :param args: 位置参数
        :param kwargs: 关键字参数
        :return: Future对象
        """
        if self._shutdown:
            raise RuntimeError("线程池已关闭")
        
        with self._lock:
            self._task_counter += 1
            task_id = self._task_counter
            
            future = Future()
            self._futures[task_id] = future
            
            # 将任务放入队列
            self._task_queue.put((task_id, func, args, kwargs, future))
            
            return future
    
    def submit_to_all(self, func: Callable, *args, **kwargs) -> List[Future]:
        """
        向所有工作线程提交相同任务
        
        :param func: 要执行的函数
        :param args: 位置参数
        :param kwargs: 关键字参数
        :return: Future对象列表
        """
        futures = []
        for _ in range(self.max_workers):
            future = self.submit(func, *args, **kwargs)
            futures.append(future)
        return futures
    
    def pause_all(self) -> bool:
        """暂停所有工作线程"""
        success_count = 0
        for thread in self._threads:
            if thread.pause():
                success_count += 1
        return success_count == len(self._threads)
    
    def resume_all(self) -> bool:
        """恢复所有工作线程"""
        success_count = 0
        for thread in self._threads:
            if thread.resume():
                success_count += 1
        return success_count == len(self._threads)
    
    def destroy_all(self) -> bool:
        """销毁所有工作线程"""
        self._shutdown = True

        # 先恢复所有被暂停的线程，确保它们能响应销毁信号
        paused_threads = []
        for thread in self._threads:
            if thread.get_state() == ThreadState.PAUSED:
                paused_threads.append(thread)
                thread.resume()

        # 给恢复的线程一点时间来响应
        if paused_threads:
            time.sleep(0.2)

        # 现在安全地销毁所有线程
        success_count = 0
        for thread in self._threads:
            try:
                if thread.destroy():
                    success_count += 1
            except Exception as e:
                print(f"销毁线程 {thread.name} 时发生异常: {e}")
                success_count += 1  # 即使异常也算作成功，因为线程可能已经结束

        self._threads.clear()
        return success_count > 0
    
    def get_all_states(self) -> Dict[str, ThreadState]:
        """获取所有线程的状态"""
        states = {}
        for thread in self._threads:
            states[thread.name] = thread.get_state()
        return states
    
    def get_active_count(self) -> int:
        """获取活跃线程数量"""
        count = 0
        for thread in self._threads:
            if thread.get_state() == ThreadState.RUNNING:
                count += 1
        return count
    
    def get_pending_tasks(self) -> int:
        """获取待处理任务数量"""
        return self._task_queue.qsize()
    
    def wait_all_complete(self, timeout: Optional[float] = None) -> bool:
        """
        等待所有任务完成
        
        :param timeout: 超时时间（秒），None表示无限等待
        :return: 是否所有任务都完成
        """
        try:
            # 等待队列中的所有任务完成
            if timeout:
                start_time = time.time()
                while not self._task_queue.empty():
                    if time.time() - start_time > timeout:
                        return False
                    time.sleep(0.1)
            else:
                self._task_queue.join()
            
            # 等待所有Future完成
            with self._lock:
                futures_copy = list(self._futures.values())
            
            for future in futures_copy:
                if timeout:
                    remaining_time = timeout - (time.time() - start_time) if timeout else None
                    if remaining_time and remaining_time <= 0:
                        return False
                    try:
                        future.result(timeout=remaining_time)
                    except:
                        pass  # 忽略任务执行异常，只关心是否完成
                else:
                    try:
                        future.result()
                    except:
                        pass
            
            return True
            
        except Exception as e:
            print(f"等待任务完成时发生异常: {e}")
            return False
    
    def shutdown(self, wait: bool = True) -> bool:
        """
        关闭线程池
        
        :param wait: 是否等待所有任务完成
        :return: 是否成功关闭
        """
        if wait:
            self.wait_all_complete()
        
        return self.destroy_all()
    
    def add_sync_threads(self, master_index: int = 0):
        """
        设置线程同步调动关系，指定主线程，其他线程跟随主线程
        
        :param master_index: 主线程索引
        """
        if 0 <= master_index < len(self._threads):
            master_thread = self._threads[master_index]
            for i, thread in enumerate(self._threads):
                if i != master_index:
                    master_thread.add_sync_thread(thread)
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown(wait=True)
    
    def __del__(self):
        """析构函数"""
        if not self._shutdown:
            self.destroy_all()


class TaskResult:
    """任务结果包装类"""
    
    def __init__(self, task_id: int, future: Future):
        self.task_id = task_id
        self.future = future
    
    def result(self, timeout: Optional[float] = None):
        """获取任务结果"""
        return self.future.result(timeout=timeout)
    
    def done(self) -> bool:
        """检查任务是否完成"""
        return self.future.done()
    
    def cancelled(self) -> bool:
        """检查任务是否被取消"""
        return self.future.cancelled()
    
    def exception(self, timeout: Optional[float] = None):
        """获取任务异常"""
        return self.future.exception(timeout=timeout)


def example_task(name: str, duration: float = 1.0) -> str:
    """示例任务函数"""
    print(f"任务 {name} 开始执行")
    time.sleep(duration)
    print(f"任务 {name} 执行完成")
    return f"任务 {name} 的结果"


if __name__ == "__main__":
    # 示例用法
    with MyThreadPool(max_workers=3, thread_name_prefix="TestPool") as pool:
        # 提交多个任务
        futures = []
        for i in range(5):
            future = pool.submit(example_task, f"任务{i}", 2.0)
            futures.append(future)
        
        print("所有任务已提交")
        
        # 暂停所有线程
        time.sleep(1)
        print("暂停所有线程")
        pool.pause_all()
        pool.destroy_all()
        time.sleep(3)
        print("恢复所有线程")
        pool.resume_all()
        
        # 等待所有任务完成
        print("等待所有任务完成...")
        pool.wait_all_complete()
        
        # 获取结果
        for i, future in enumerate(futures):
            try:
                result = future.result()
                print(f"任务{i}结果: {result}")
            except Exception as e:
                print(f"任务{i}异常: {e}")
