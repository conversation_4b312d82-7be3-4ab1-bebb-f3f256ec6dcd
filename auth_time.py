import datetime
import ntplib
import os
import mythread
import time
import subprocess
import sys
import ctypes

def get_short_path(long_path):
    """将带中文或空格的路径转换为短路径名（8.3格式）"""
    buf = ctypes.create_unicode_buffer(512)
    ctypes.windll.kernel32.GetShortPathNameW(long_path, buf, len(buf))
    return buf.value

def get_time_str():
    return datetime.datetime.now().strftime('%H:%M:%S')

def auth_time():
    try:
        client = ntplib.NTPClient()
        # 尝试多个NTP服务器
        ntp_servers = ['pool.ntp.org', 'time.windows.com', 'time.nist.gov', 'cn.pool.ntp.org']
        for server in ntp_servers:
            try:
                response = client.request(server, timeout=5)
                timestamp = response.tx_time
                return datetime.datetime.fromtimestamp(timestamp)
            except:
                continue
        return 0
    except Exception as e:
        print(e)
        return 0

def delete_self():
    # 获取当前脚本或exe的路径
    if getattr(sys, 'frozen', False):
        # 如果是打包的exe
        exe_path = sys.executable
    else:
        # 如果是Python脚本
        exe_path = os.path.abspath(__file__)

    bat_path = os.path.join(os.path.dirname(exe_path), "delete_temp.bat")

    # 获取短路径以兼容中文或空格
    try:
        short_exe = get_short_path(exe_path)
        short_bat = get_short_path(bat_path)
    except:
        # 如果获取短路径失败，使用原路径
        short_exe = exe_path
        short_bat = bat_path

    # 构建bat脚本，延迟+删除exe+自删bat
    bat_content = f"""@echo off
chcp 65001 > nul
ping 127.0.0.1 -n 3 > nul
if exist "{short_exe}" del /f /q "{short_exe}"
if exist "{short_bat}" del /f /q "{short_bat}"
exit
"""

    try:
        # 写入bat文件
        with open(bat_path, "w", encoding="gbk") as f:
            f.write(bat_content)

        # 启动bat脚本（无控制台，不阻塞）
        subprocess.Popen([short_bat], shell=True, creationflags=subprocess.CREATE_NO_WINDOW)

        # 立即退出当前进程
        time.sleep(1)
        os._exit(0)

    except Exception as e:
        print(f"删除失败: {e}")
        os._exit(1)

def check_expiry_and_exit(year, month, day):
    """
    检查授权到期时间
    参数:
        year (int): 年份
        month (int): 月份
        day (int): 日期
    """
    while True:
        当前时间 = auth_time()
        if 当前时间 == 0:
            # print("授权时间获取失败，程序即将退出...")
            delete_self()
            time.sleep(2)
            os._exit(1)

        # 检查日期是否超过指定日期
        # 如果年份超过指定年份，直接退出
        if 当前时间.year > year:
            # print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
            delete_self()
            time.sleep(2)
            os._exit(1)
        # 如果是指定年份，检查月份和日期
        elif 当前时间.year == year:
            if 当前时间.month > month or (当前时间.month == month and 当前时间.day > day):
                # print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
                delete_self()
                time.sleep(2)
                os._exit(1)

        # 每60秒检查一次
        time.sleep(10)
if __name__ == "__main__":
    print(auth_time())