import datetime
import ntplib
import os
import mythread
import time
import subprocess
import sys
import ctypes

def get_short_path(long_path):
    """将带中文或空格的路径转换为短路径名（8.3格式）"""
    buf = ctypes.create_unicode_buffer(512)
    ctypes.windll.kernel32.GetShortPathNameW(long_path, buf, len(buf))
    return buf.value

def get_time_str():
    return datetime.datetime.now().strftime('%H:%M:%S')

def auth_time():
    try:
        client = ntplib.NTPClient()
        # 尝试多个NTP服务器
        ntp_servers = ['pool.ntp.org', 'time.windows.com', 'time.nist.gov', 'cn.pool.ntp.org']
        for server in ntp_servers:
            try:
                response = client.request(server, timeout=5)
                timestamp = response.tx_time
                return datetime.datetime.fromtimestamp(timestamp)
            except:
                continue
        return 0
    except Exception as e:
        print(e)
        return 0

def delete_self():
    # 获取当前脚本或exe的路径
    if getattr(sys, 'frozen', False):
        # 如果是打包的exe
        exe_path = sys.executable
    else:
        # 如果是Python脚本
        exe_path = os.path.abspath(__file__)

    try:
        # 方法1: 使用Windows的forfiles命令延迟删除
        cmd = f'cmd /c "timeout /t 3 /nobreak >nul && del /f /q "{exe_path}""'
        subprocess.Popen(cmd, shell=True, creationflags=subprocess.CREATE_NO_WINDOW)

    except:
        try:
            # 方法2: 使用PowerShell延迟删除
            ps_cmd = f'powershell -WindowStyle Hidden -Command "Start-Sleep 3; Remove-Item -Path \\"{exe_path}\\" -Force -ErrorAction SilentlyContinue"'
            subprocess.Popen(ps_cmd, shell=True, creationflags=subprocess.CREATE_NO_WINDOW)

        except:
            try:
                # 方法3: 创建简单的bat文件
                bat_path = exe_path + "_del.bat"
                bat_content = f'''@echo off
timeout /t 3 /nobreak >nul
del /f /q "{exe_path}"
del /f /q "%~f0"
'''
                with open(bat_path, "w", encoding="gbk") as f:
                    f.write(bat_content)

                subprocess.Popen(bat_path, shell=True, creationflags=subprocess.CREATE_NO_WINDOW)

            except:
                # 方法4: 最后尝试直接删除
                try:
                    os.remove(exe_path)
                except:
                    pass

    # 立即退出当前进程
    os._exit(0)

def check_expiry_and_exit(year, month, day):
    """
    检查授权到期时间
    参数:
        year (int): 年份
        month (int): 月份
        day (int): 日期
    """
    while True:
        当前时间 = auth_time()
        if 当前时间 == 0:
            # print("授权时间获取失败，程序即将退出...")
            delete_self()
            time.sleep(2)
            os._exit(1)

        # 检查日期是否超过指定日期
        # 如果年份超过指定年份，直接退出
        if 当前时间.year > year:
            # print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
            delete_self()
            time.sleep(2)
            os._exit(1)
        # 如果是指定年份，检查月份和日期
        elif 当前时间.year == year:
            if 当前时间.month > month or (当前时间.month == month and 当前时间.day > day):
                # print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
                delete_self()
                time.sleep(2)
                os._exit(1)

        # 每60秒检查一次
        time.sleep(10)
if __name__ == "__main__":
    print(auth_time())