import datetime
import ntplib
import os
import mythread
import time
import subprocess
import sys
import ctypes

def get_short_path(long_path):
    """将带中文或空格的路径转换为短路径名（8.3格式）"""
    buf = ctypes.create_unicode_buffer(512)
    ctypes.windll.kernel32.GetShortPathNameW(long_path, buf, len(buf))
    return buf.value

def get_time_str():
    return datetime.datetime.now().strftime('%H:%M:%S')

def auth_time():
    try:
        client = ntplib.NTPClient()
        # 尝试多个NTP服务器
        ntp_servers = ['pool.ntp.org', 'time.windows.com', 'time.nist.gov', 'cn.pool.ntp.org']
        for server in ntp_servers:
            try:
                response = client.request(server, timeout=5)
                timestamp = response.tx_time
                return datetime.datetime.fromtimestamp(timestamp)
            except:
                continue
        return 0
    except Exception as e:
        print(e)
        return 0

def delete_self():
    # 获取当前脚本或exe的路径
    if getattr(sys, 'frozen', False):
        # 如果是打包的exe
        exe_path = sys.executable
    else:
        # 如果是Python脚本
        exe_path = os.path.abspath(__file__)

    try:
        # 创建一个新的Python进程来删除当前文件
        delete_script = f'''
import os
import time
import sys

# 等待原进程完全退出
time.sleep(2)

# 尝试删除文件
try:
    if os.path.exists(r"{exe_path}"):
        os.remove(r"{exe_path}")
        print("文件删除成功")
except Exception as e:
    print(f"删除失败: {{e}}")

# 删除自身（这个临时脚本）
try:
    os.remove(__file__)
except:
    pass
'''

        # 创建临时删除脚本
        temp_script = os.path.join(os.path.dirname(exe_path), "temp_delete.py")
        with open(temp_script, "w", encoding="utf-8") as f:
            f.write(delete_script)

        # 启动新的Python进程执行删除脚本
        subprocess.Popen([sys.executable, temp_script],
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL)

        # 立即退出当前进程
        os._exit(0)

    except Exception as e:
        # 如果Python方式失败，回退到直接删除
        try:
            # 尝试直接删除（通常会失败，因为文件正在使用）
            os.remove(exe_path)
        except:
            pass
        os._exit(1)

def check_expiry_and_exit(year, month, day):
    """
    检查授权到期时间
    参数:
        year (int): 年份
        month (int): 月份
        day (int): 日期
    """
    while True:
        当前时间 = auth_time()
        if 当前时间 == 0:
            # print("授权时间获取失败，程序即将退出...")
            delete_self()
            time.sleep(2)
            os._exit(1)

        # 检查日期是否超过指定日期
        # 如果年份超过指定年份，直接退出
        if 当前时间.year > year:
            # print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
            delete_self()
            time.sleep(2)
            os._exit(1)
        # 如果是指定年份，检查月份和日期
        elif 当前时间.year == year:
            if 当前时间.month > month or (当前时间.month == month and 当前时间.day > day):
                # print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
                delete_self()
                time.sleep(2)
                os._exit(1)

        # 每60秒检查一次
        time.sleep(10)
if __name__ == "__main__":
    print(auth_time())