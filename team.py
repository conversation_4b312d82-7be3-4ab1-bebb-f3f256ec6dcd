from myj import MYJ
import time
from map import ret_way
import datetime
from concurrent

config = {
    "服务器": "s69",
    "账号": "yfydq1",
    "密码": "yfydq0101",
    "角色序号": "0",    
}

class MYJTeam:
    def __init__(self, configs):
        self.myj_objects = [MYJ(config) for config in configs]
    
    def 单角色执行命令(self,s,index=0):
        self.myj_objects[index].send_orders(s)

    def 全体执行命令(self,s,delay=None):
        局_命令列表 = s.split('|')
        for 局_单条命令 in 局_命令列表:
            for myj in self.myj_objects:
                myj.send_orders(局_单条命令,0)
            if delay is not None:
                time.sleep(delay)
            else:
                time.sleep(self.myj_objects[0].延迟)

    def gto_异地(self, target):
        for myj in self.myj_objects:
            myj.gto(target)

    def gto_异地_序号目标(self,index):
        pass

    def gto_同地(self,target):
        局_跑图命令 = ret_way(self.myj_objects[0].地图 + '|' + self.myj_objects[0].房间, target)
        self.全体执行命令(局_跑图命令)
        self.gto_异地(target)

    def gto_同地_序号目标(self,index):
        pass        

    def 组队(self):
        for index in range(len(self.myj_objects)):
            if index == 0:
                pass
            else:
                self.myj_objects[0].send_orders(f'foo rank add {self.myj_objects[index].宠物名}',0)
                self.myj_objects[index].send_orders(f'foo rank agree {self.myj_objects[0].petId}|follow none|follow {self.myj_objects[0].宠物名}')

    def 悬赏(self):
        while True:
            # 外层无限循环
            # 走到拖把皇宫大门
            self.gto_异地('map.mopcity|皇宫大门')
            break

def 哈刚():
    configs = [
        {
            "服务器": "x12",
            "账号": "kikyou514",
            "密码": "ly301211",
            "角色序号": "0",    
        },
                {
            "服务器": "x12",
            "账号": "kikyou515",
            "密码": "ly301211",
            "角色序号": "0",    
        },
                {
            "服务器": "x12",
            "账号": "kikyou516",
            "密码": "ly301211",
            "角色序号": "0",    
        },
    ]
    team = MYJTeam(configs)
    team.gto_异地('map.mopcity|石之谜')
    while True:
        t = datetime.datetime.now()
        while t.minute >= 59 or t.minute <= 1:   
            team.全体执行命令('buyGang',0.28)
            t = datetime.datetime.now()
        time.sleep(10)
    input()

if __name__ == '__main__':
    pass